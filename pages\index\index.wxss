/**index.wxss**/
page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.scrollarea {
  flex: 1;
  overflow-y: auto;
}

.container {
  padding: 0;
  box-sizing: border-box;
  width: 100%;
}

/* 搜索框样式 */
.search-box {
  width: 100%;
  margin-bottom: 15px;
}

.search-input-wrapper {
  height: 40px;
  background-color: #f5f5f5;
  border-radius: 20px;
  display: flex;
  align-items: center;
  padding: 0 15px;
}

.search-icon {
  width: 16px;
  height: 16px;
  margin-right: 8px;
}

.search-placeholder {
  color: #999;
  font-size: 14px;
}

.userinfo {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #aaa;
  width: 80%;
}

.userinfo-avatar {
  overflow: hidden;
  width: 128rpx;
  height: 128rpx;
  margin: 20rpx;
  border-radius: 50%;
}

.usermotto {
  margin-top: 200px;
}

.avatar-wrapper {
  padding: 0;
  width: 56px !important;
  border-radius: 8px;
  margin-top: 40px;
  margin-bottom: 40px;
}

.avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
}

.nickname-wrapper {
  display: flex;
  width: 100%;
  padding: 16px;
  box-sizing: border-box;
  border-top: .5px solid rgba(0, 0, 0, 0.1);
  border-bottom: .5px solid rgba(0, 0, 0, 0.1);
  color: black;
}

.nickname-label {
  width: 105px;
}

.nickname-input {
  flex: 1;
}

.controls {
  margin-top: 20px;
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  justify-content: center;
}

.controls button {
  min-width: 120px;
  margin: 0;
}

.status {
  margin-top: 20px;
  text-align: center;
  color: #666;
}

/* 自定义打字机组件样式 */
.typewriter-text {
  font-size: 16px;
  line-height: 1.6;
  color: #333;
  background-color: #f8f8f8;
  padding: 15px;
  border-radius: 8px;
  min-height: 100px;
}

.camera-btn {
  margin: 20rpx;
  background-color: #07c160;
  color: #ffffff;
  border-radius: 10rpx;
  padding: 20rpx 40rpx;
}

.function-buttons {
  padding: 20rpx;
}

.function-btn {
  width: 100%;
  height: 160rpx;
  background: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin-bottom: 20rpx;
  padding: 20rpx;
  box-sizing: border-box;
}

.btn-text {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.btn-desc {
  font-size: 24rpx;
  color: #999;
}

/* 登录按钮样式 */
.login-button-container {
  width: 100%;
  display: flex;
  justify-content: center;
  margin: 20rpx 0;
}

.login-button {
  background-color: #4F46E5;
  color: #FFFFFF;
  width: 80%;
  height: 80rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0px 4rpx 8rpx -4rpx rgba(0, 0, 0, 0.1),
    0px 8rpx 12rpx -2rpx rgba(0, 0, 0, 0.1);
}

/* 顶部标题栏样式 */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
  background-color: #ffffff;
  width: 100%;
  box-sizing: border-box;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 100;
  border-bottom: 1rpx solid #f0f0f0;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.header-left {
  display: flex;
  align-items: center;
}

.logo {
  width: 60rpx;
  height: 60rpx;
  margin-right: 10rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.header-btn {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: #f8f8f8;
  transition: all 0.3s ease;
}

.header-btn:active {
  background-color: #e8e8e8;
  transform: scale(0.95);
}

/* 健康卡片轮播样式 */
.health-card-swiper {
  height: 240rpx;
  margin: 20rpx 20rpx 30rpx 20rpx;
  width: calc(100% - 40rpx);
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);
  /* 优化轮播图性能 */
  transform: translateZ(0);
  contain: layout style paint;
  isolation: isolate;
}

/* 轮播图项目样式 */
.health-card-swiper swiper-item {
  width: 100%;
  height: 100%;
  border-radius: 24rpx;
  overflow: hidden;
  position: relative;
}

/* 轮播图点击区域 */
.banner-item {
  width: 100%;
  height: 100%;
  position: relative;
  cursor: pointer;
}

/* 轮播图指示器样式 */
.health-card-swiper .wx-swiper-dots.wx-swiper-dots-horizontal {
  margin-bottom: 16rpx;
}

.health-card-swiper .wx-swiper-dot {
  width: 16rpx;
  height: 6rpx;
  border-radius: 3rpx;
  background-color: rgba(255, 255, 255, 0.4);
  margin: 0 6rpx;
}

.health-card-swiper .wx-swiper-dot-active {
  background-color: rgba(255, 255, 255, 0.9);
  width: 32rpx;
}

/* 养生朋克卡片样式 */
.health-card {
  background: transparent;
  border-radius: 20rpx;
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
  overflow: hidden;
  position: relative;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  /* 优化性能，避免重绘 */
  backface-visibility: hidden;
  transform: translateZ(0);
}

/* 第二张卡片样式 */
.health-card-second {
  background: transparent;
}

/* 第三张卡片样式 */
.health-card-third {
  background: transparent;
}

.health-card-content {
  color: white;
  z-index: 2;
  position: relative;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.5);
}

.health-card-title {
  font-size: 40rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.health-card-subtitle {
  font-size: 24rpx;
  opacity: 0.9;
  margin-bottom: 30rpx;
}

.health-card-btn {
  background-color: #ffffff;
  color: #6a1b9a;
  font-size: 24rpx;
  padding: 8rpx 30rpx;
  border-radius: 30rpx;
  display: inline-block;
  font-weight: bold;
  line-height: 1.8;
  width: auto;
  min-width: 160rpx;
}

.health-card-second .health-card-btn {
  color: #00acc1;
}

.health-card-third .health-card-btn {
  color: #4caf50;
}

.health-card-image {
  width: 100%;
  height: 100%;
  display: block;
  border-radius: 24rpx;
}

/* 功能导航区域样式 - 科技感增强版 */
.function-nav {
  box-sizing: border-box;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  height: 424rpx;
  touch-action: none;
  overflow: hidden;
  /* 启用硬件加速 */
  transform: translateZ(0);
  will-change: transform;
  /* 确保没有背景遮挡 */
  background: transparent;
}
.robot{
  position: absolute;
  width: 160rpx;
  height: 160rpx;
  top: 34%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 2;
}

.function-nav-bg {
  position: absolute;
  width: 100%;
  height: 424rpx;
  z-index: 0;
  /* 移除暗化滤镜，保持原始亮度 */
  filter: none;
  /* 优化背景渲染 */
  transform: translateZ(0);
}

.function-nav-content {
  position: relative;
  z-index: 1;
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  /* 启用硬件加速 */
  transform: translateZ(0);
}

/* 椭圆环形菜单样式 - 星系旋转升级版 */
.elliptical-menu {
  position: relative;
  width: 100%;
  height: 100%;
  transform-style: preserve-3d;
  perspective: 1000rpx;
  transition: transform 0.3s ease-out;
  overflow: visible;
  /* 启用硬件加速，优化星系旋转性能 */
  will-change: transform;
  transform: translateZ(0);
}

/* 手动滑动状态的特殊样式 */
.elliptical-menu.galaxy-rotating {
  /* 滑动时加强性能优化 */
  transform: translateZ(0);
}

.elliptical-menu.galaxy-rotating .menu-item {
  /* 滑动时禁用过渡效果，让滑动更跟手 */
  transition: none !important;
  backface-visibility: hidden;
  transform: translateZ(0);
  /* 滑动时保持渐变效果但禁用过渡 */
  will-change: transform, opacity, filter;
}

/* 惯性滑动状态的平滑样式 */
.elliptical-menu.inertia-scrolling {
  /* 惯性滑动时启用平滑过渡 */
  transition: none;
}

.elliptical-menu.inertia-scrolling .menu-item {
  /* 惯性滑动时菜单项使用轻微的过渡效果 */
  transition: opacity 0.1s ease-out, filter 0.1s ease-out;
  backface-visibility: hidden;
  transform: translateZ(0);
  will-change: transform, opacity, filter;
}

/* 优化的椭圆轨道线（减少遮挡） */
.elliptical-menu::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 70%;
  height: 50%;
  border: 1rpx solid rgba(64, 224, 255, 0.15);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: simple-glow 3s ease-in-out infinite;
  pointer-events: none;
  z-index: -5;
  /* 降低层级，避免遮挡 */
  opacity: 0.3;
  /* 进一步降低透明度 */
}

/* 隐藏粒子轨道线（手机端可能不支持） */
.elliptical-menu::after {
  display: none;
}

/* ==================== 传统文化与科技融合动画系统 ==================== */

/* 太极阴阳背景系统 */
.taiji-background {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 200rpx;
  height: 200rpx;
  transform: translate(-50%, -50%);
  z-index: -10;
  /* 降低层级，避免遮挡 */
  opacity: 0.08;
  /* 进一步降低透明度，减少灰蒙感 */
  pointer-events: none;
  /* 确保不阻挡交互 */
}

.taiji-container {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  overflow: hidden;
  animation: taiji-rotation 20s linear infinite;
}

.taiji-image {
  width: 80%;
  height: 80%;
  border-radius: 50%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: block;
  filter: brightness(1.3) contrast(1.1);
}

.taiji-yin {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 50%;
  background: linear-gradient(180deg, #000 0%, #333 100%);
  border-radius: 100% 100% 0 0;
}

.taiji-yang {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 50%;
  background: linear-gradient(0deg, #fff 0%, #f0f0f0 100%);
  border-radius: 0 0 100% 100%;
}

.taiji-dot {
  position: absolute;
  width: 25%;
  height: 25%;
  border-radius: 50%;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.taiji-dot-yin {
  background: #000;
  top: 25%;
}

.taiji-dot-yang {
  background: #fff;
  top: 75%;
}

@keyframes taiji-rotation {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

/* 五行能量环系统 */
.wuxing-energy-ring {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 300rpx;
  height: 180rpx;
  transform: translate(-50%, -50%);
  z-index: -8;
  /* 降低层级 */
  pointer-events: none;
  /* 确保不阻挡交互 */
}

.energy-ring {
  position: absolute;
  top: 50%;
  left: 50%;
  border-radius: 50%;
  border: 2rpx solid transparent;
  transform: translate(-50%, -50%);
  opacity: 0.4;
  /* 降低透明度，减少视觉干扰 */
  animation: energy-ring-pulse 4s ease-in-out infinite;
}

.energy-ring-wood {
  width: 280rpx;
  height: 160rpx;
  border-color: rgba(34, 197, 94, 0.3);
  animation-delay: 0s;
}

.energy-ring-fire {
  width: 260rpx;
  height: 140rpx;
  border-color: rgba(239, 68, 68, 0.3);
  animation-delay: 0.8s;
}

.energy-ring-earth {
  width: 240rpx;
  height: 120rpx;
  border-color: rgba(245, 158, 11, 0.3);
  animation-delay: 1.6s;
}

.energy-ring-metal {
  width: 220rpx;
  height: 100rpx;
  border-color: rgba(156, 163, 175, 0.25);
  animation-delay: 2.4s;
}

.energy-ring-water {
  width: 200rpx;
  height: 80rpx;
  border-color: rgba(59, 130, 246, 0.3);
  animation-delay: 3.2s;
}

@keyframes energy-ring-pulse {

  0%,
  100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.6;
  }

  50% {
    transform: translate(-50%, -50%) scale(1.1);
    opacity: 0.8;
  }
}

/* 星宿轨道系统 */
.constellation-system {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  transform: translate(-50%, -50%);
  z-index: -6;
  /* 调整层级 */
  pointer-events: none;
  /* 确保不阻挡交互 */
}

.orbit-layer {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border-radius: 50%;
}

.orbit-inner {
  width: 180rpx;
  height: 100rpx;
  animation: constellation-rotate 15s linear infinite;
}

.orbit-middle {
  width: 280rpx;
  height: 160rpx;
  animation: constellation-rotate 25s linear infinite reverse;
}

.orbit-outer {
  width: 380rpx;
  height: 220rpx;
  animation: constellation-rotate 35s linear infinite;
}

.star-particle {
  position: absolute;
  width: 6rpx;
  height: 6rpx;
  background: radial-gradient(circle, #fff 0%, rgba(255, 255, 255, 0.2) 50%, transparent 100%);
  border-radius: 50%;
  opacity: 0.6;
  /* 降低透明度，减少视觉干扰 */
  animation: star-twinkle 2s ease-in-out infinite;
}

/* 星粒子分布 */
.star-1 {
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  animation-delay: 0s;
}

.star-2 {
  top: 50%;
  right: 0;
  transform: translateY(-50%);
  animation-delay: 0.7s;
}

.star-3 {
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  animation-delay: 1.4s;
}

.star-4 {
  top: 15%;
  right: 15%;
  animation-delay: 0.3s;
}

.star-5 {
  top: 85%;
  right: 15%;
  animation-delay: 1.0s;
}

.star-6 {
  top: 85%;
  left: 15%;
  animation-delay: 1.7s;
}

.star-7 {
  top: 15%;
  left: 15%;
  animation-delay: 2.4s;
}

.star-8 {
  top: 30%;
  right: 5%;
  animation-delay: 0.5s;
}

.star-9 {
  top: 70%;
  left: 5%;
  animation-delay: 1.2s;
}

@keyframes constellation-rotate {
  from {
    transform: translate(-50%, -50%) rotate(0deg);
  }

  to {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

@keyframes star-twinkle {

  0%,
  100% {
    opacity: 0.3;
    transform: scale(1);
  }

  50% {
    opacity: 1;
    transform: scale(1.2);
  }
}

/* 能量流转线条系统 */
.energy-flow-system {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  transform: translate(-50%, -50%);
  z-index: -2;
  /* 降低层级，避免遮挡菜单项 */
  pointer-events: none;
}

.energy-line {
  position: absolute;
  height: 2rpx;
  background: linear-gradient(90deg, transparent 0%, rgba(64, 224, 255, 0.6) 50%, transparent 100%);
  border-radius: 1rpx;
  opacity: 0;
  animation: energy-flow 2s ease-in-out;
}

.energy-line-1 {
  top: 30%;
  left: 20%;
  width: 60%;
  transform: rotate(45deg);
  animation-delay: 0s;
}

.energy-line-2 {
  top: 50%;
  left: 15%;
  width: 70%;
  transform: rotate(-30deg);
  animation-delay: 0.3s;
}

.energy-line-3 {
  top: 70%;
  left: 25%;
  width: 50%;
  transform: rotate(60deg);
  animation-delay: 0.6s;
}

@keyframes energy-flow {
  0% {
    opacity: 0;
    transform: scale(0.5);
  }

  50% {
    opacity: 1;
    transform: scale(1);
  }

  100% {
    opacity: 0;
    transform: scale(1.2);
  }
}

/* 生命呼吸核心 */
.life-core {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 40rpx;
  height: 40rpx;
  transform: translate(-50%, -50%);
  z-index: -4;
  /* 调整层级 */
  pointer-events: none;
  /* 确保不阻挡交互 */
}

.core-inner,
.core-middle,
.core-outer {
  position: absolute;
  top: 50%;
  left: 50%;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: life-breathing 4s ease-in-out infinite;
}

.core-inner {
  width: 20rpx;
  height: 20rpx;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.8) 0%, transparent 70%);
  animation-delay: 0s;
}

.core-middle {
  width: 30rpx;
  height: 30rpx;
  background: radial-gradient(circle, rgba(64, 224, 255, 0.4) 0%, transparent 70%);
  animation-delay: 0.5s;
}

.core-outer {
  width: 40rpx;
  height: 40rpx;
  background: radial-gradient(circle, rgba(64, 224, 255, 0.2) 0%, transparent 70%);
  animation-delay: 1s;
}

@keyframes life-breathing {

  0%,
  100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.6;
  }

  50% {
    transform: translate(-50%, -50%) scale(1.3);
    opacity: 1;
  }
}

/* 菜单项通用样式 - 滚动缩放版本 + 渐变动画效果 */
.menu-item {
  position: absolute;
  width: 81rpx;
  height: 81rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  /* 添加平滑的缩放过渡效果和渐变动画 */
  transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94),
    opacity 0.25s ease-out,
    filter 0.25s ease-out,
    z-index 0s;
  transform-origin: center;
  z-index: 2;
  cursor: pointer;
  user-select: none;
  /* 启用硬件加速，提升滑动性能 */
  will-change: transform, opacity, filter;
}

/* 五行光晕系统 */
.wuxing-aura {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  opacity: 0.3;
  z-index: 0;
  /* 调整层级，在菜单图标下方但在背景之上 */
  transition: all 0.4s ease;
  animation: wuxing-aura-pulse 3s ease-in-out infinite;
  pointer-events: none;
  /* 确保不阻挡点击 */
}

.wuxing-wood {
  background: radial-gradient(circle, rgba(34, 197, 94, 0.3) 0%, rgba(34, 197, 94, 0.1) 50%, transparent 80%);
  box-shadow: 0 0 30rpx rgba(34, 197, 94, 0.2);
}

.wuxing-fire {
  background: radial-gradient(circle, rgba(239, 68, 68, 0.3) 0%, rgba(239, 68, 68, 0.1) 50%, transparent 80%);
  box-shadow: 0 0 30rpx rgba(239, 68, 68, 0.2);
}

.wuxing-earth {
  background: radial-gradient(circle, rgba(245, 158, 11, 0.3) 0%, rgba(245, 158, 11, 0.1) 50%, transparent 80%);
  box-shadow: 0 0 30rpx rgba(245, 158, 11, 0.2);
}

.wuxing-metal {
  background: radial-gradient(circle, rgba(156, 163, 175, 0.3) 0%, rgba(156, 163, 175, 0.1) 50%, transparent 80%);
  box-shadow: 0 0 30rpx rgba(156, 163, 175, 0.2);
}

.wuxing-water {
  background: radial-gradient(circle, rgba(59, 130, 246, 0.3) 0%, rgba(59, 130, 246, 0.1) 50%, transparent 80%);
  box-shadow: 0 0 30rpx rgba(59, 130, 246, 0.2);
}

@keyframes wuxing-aura-pulse {

  0%,
  100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.3;
  }

  50% {
    transform: translate(-50%, -50%) scale(1.1);
    opacity: 0.5;
  }
}

/* 涟漪扩散效果系统 */
.ripple-effect {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100rpx;
  height: 100rpx;
  transform: translate(-50%, -50%);
  pointer-events: none;
  z-index: 1;
  /* 提高层级，确保涟漪效果可见 */
}

.ripple-circle {
  position: absolute;
  top: 50%;
  left: 50%;
  border-radius: 50%;
  border: 2rpx solid rgba(64, 224, 255, 0.6);
  transform: translate(-50%, -50%);
  animation: ripple-expand 0.8s ease-out forwards;
}

.ripple-1 {
  width: 100rpx;
  height: 100rpx;
  animation-delay: 0s;
}

.ripple-2 {
  width: 100rpx;
  height: 100rpx;
  animation-delay: 0.1s;
}

.ripple-3 {
  width: 100rpx;
  height: 100rpx;
  animation-delay: 0.2s;
}

@keyframes ripple-expand {
  0% {
    width: 100rpx;
    height: 100rpx;
    opacity: 1;
    border-width: 2rpx;
  }

  100% {
    width: 200rpx;
    height: 200rpx;
    opacity: 0;
    border-width: 0rpx;
  }
}

/* 为每个菜单项添加科技感光环 */
.menu-item::before {
  content: '';
  position: absolute;
  width: 140rpx;
  height: 140rpx;
  background: radial-gradient(circle, transparent 30%, rgba(64, 224, 255, 0.03) 40%, transparent 70%);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  top: 50%;
  left: 50%;
  transition: all 0.4s ease;
  z-index: -1;
  /* 调整层级 */
  pointer-events: none;
}

/* 添加第二层发散光环 */
.menu-item::after {
  content: '';
  position: absolute;
  width: 160rpx;
  height: 160rpx;
  background: radial-gradient(circle, transparent 40%, rgba(64, 224, 255, 0.01) 50%, transparent 80%);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  top: 50%;
  left: 50%;
  transition: all 0.4s ease;
  z-index: -1;
  /* 调整层级 */
  opacity: 0;
  pointer-events: none;
}

.menu-icon {
  width: 101rpx;
  height: 101rpx;
  background-color: transparent;
  border-radius: 50%;
  padding: 0;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  box-shadow: none;
  filter: drop-shadow(0 4rpx 8rpx rgba(0, 0, 0, 0.1));
  position: relative;
  z-index: 1;
  /* 为图标旋转添加平滑过渡，避免卡顿 */
  transition: transform 0.1s linear, filter 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

/* 连接节点效果已移除（手机端兼容优化） */

/* 精确的激活状态 - 只影响当前选中项 */
.menu-item.active {
  transform: translate(-50%, -50%) scale(1.15) !important;
  z-index: 15 !important;
}

.menu-item.active>.menu-icon {
  filter: brightness(1.3) drop-shadow(0 0 15rpx rgba(64, 224, 255, 0.8)) !important;
  animation: enhanced-icon-glow 1.8s ease-in-out infinite !important;
}

.menu-item.active>.wuxing-aura {
  opacity: 0.9 !important;
  transform: translate(-50%, -50%) scale(1.2) !important;
  animation: enhanced-aura-pulse 1.8s ease-in-out infinite !important;
}

.menu-item.active::before {
  opacity: 0.8 !important;
  transform: translate(-50%, -50%) scale(1.15) !important;
}

.menu-item.active::after {
  opacity: 0.4 !important;
  transform: translate(-50%, -50%) scale(1.1) !important;
}

/* 确保非激活状态保持正常 */
.menu-item:not(.active) {
  transform: translate(-50%, -50%) scale(1);
  z-index: 2;
}

.menu-item:not(.active)>.menu-icon {
  filter: drop-shadow(0 4rpx 8rpx rgba(0, 0, 0, 0.1));
  animation: none;
}

.menu-item:not(.active)>.wuxing-aura {
  opacity: 0.3;
  transform: translate(-50%, -50%) scale(1);
  animation: wuxing-aura-pulse 3s ease-in-out infinite;
}

/* 增强的发光动画 */
@keyframes enhanced-icon-glow {

  0%,
  100% {
    filter: brightness(1.3) drop-shadow(0 0 20rpx rgba(64, 224, 255, 0.6));
    transform: scale(1);
  }

  50% {
    filter: brightness(1.5) drop-shadow(0 0 30rpx rgba(64, 224, 255, 0.8));
    transform: scale(1.05);
  }
}

@keyframes enhanced-aura-pulse {

  0%,
  100% {
    transform: translate(-50%, -50%) scale(1.3);
    opacity: 0.8;
  }

  50% {
    transform: translate(-50%, -50%) scale(1.4);
    opacity: 1;
  }
}

/* 简化的发光动画（手机端兼容） */
@keyframes simple-glow {

  0%,
  100% {
    border-color: rgba(64, 224, 255, 0.3);
    opacity: 0.7;
  }

  50% {
    border-color: rgba(64, 224, 255, 0.6);
    opacity: 1;
  }
}

/* 简化的图标发光动画 */
@keyframes simple-icon-glow {

  0%,
  100% {
    filter: brightness(1.2);
    transform: scale(1);
  }

  50% {
    filter: brightness(1.4);
    transform: scale(1.05);
  }
}

/* 浮动动画已简化（手机端兼容优化） */

/* 菜单项过渡效果 - 增强版本 */
.menu-item-transition {
  transition: all 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.menu-item-transition .menu-item {
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

/* 菜单项动画状态 */
.menu-item.animating {
  transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* 点击反馈 - 增强版涟漪效果 */
.menu-item.ripple {
  animation: enhanced-click-feedback 0.4s ease-out;
}

/* 增强的点击反馈动画 */
@keyframes enhanced-click-feedback {
  0% {
    transform: translate(-50%, -50%) scale(1);
  }

  30% {
    transform: translate(-50%, -50%) scale(1.1);
  }

  60% {
    transform: translate(-50%, -50%) scale(0.95);
  }

  100% {
    transform: translate(-50%, -50%) scale(1);
  }
}

/* 机器人容器相关样式已移除 - 用户已删除中央机器人图标 */

/* 移除旧的简单点击反馈动画 */

/* 性能优化：减少不必要的动画层级 */

/* 响应式优化：根据设备性能调整动画 */
@media (max-width: 750px) {

  /* 在小屏设备上简化动画 */
  .constellation-system {
    opacity: 0.7;
  }

  .taiji-background {
    opacity: 0.1;
  }

  .energy-ring {
    animation-duration: 6s;
    /* 延长动画周期以节省性能 */
  }

  .star-particle {
    animation-duration: 3s;
  }
}

/* 暗黑模式适配 */
@media (prefers-color-scheme: dark) {
  .taiji-yang {
    background: linear-gradient(0deg, #333 0%, #666 100%);
  }

  .taiji-dot-yang {
    background: #666;
  }

  .life-core .core-inner {
    background: radial-gradient(circle, rgba(255, 255, 255, 0.6) 0%, transparent 70%);
  }
}

/* ==================== 临时调试样式区域 ==================== */
/* 如果还有灰蒙蒙的效果，可以逐个取消注释下面的样式来定位问题 */

/* 1. 临时隐藏太极背景 */
/*
.taiji-background {
  display: none !important;
}
*/

/* 2. 临时隐藏五行能量环 */
/*
.wuxing-energy-ring {
  display: none !important;
}
*/

/* 3. 临时隐藏星宿轨道系统 */
/*
.constellation-system {
  display: none !important;
}
*/

/* 4. 临时隐藏椭圆轨道线 */
/*
.elliptical-menu::before {
  display: none !important;
}
*/

/* 5. 临时隐藏能量流转线条 */
/*
.energy-flow-system {
  display: none !important;
}
*/

/* 6. 临时隐藏生命呼吸核心 */
/*
.life-core {
  display: none !important;
}
*/

/* 当找到问题元素后，可以删除这整个调试区域 */

.function-items {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
}

.function-row {
  display: flex;
  justify-content: space-around;
}

.function-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 160rpx;
}

.function-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 10rpx;
}

.function-text {
  font-size: 24rpx;
  color: #333;
}

/* AI体质卡样式 */
.health-analysis-card {
  margin: 20rpx;
  background-color: #ffffff;
  border-radius: 20rpx;
  overflow: hidden;
  width: calc(100% - 40rpx);
  box-sizing: border-box;
  transform: translateY(-15rpx);
}

.health-analysis-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0rpx;
  margin: 0 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.health-analysis-title {
  font-size: 32rpx;
  font-weight: bold;
}

.health-analysis-days {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #888;
}

.arrow-icon {
  margin: 0 10rpx;
}

.health-analysis-content {
  padding: 20rpx 30rpx 30rpx;
}

.health-analysis-core {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.core-title {
  font-size: 28rpx;
  font-weight: bold;
  margin-right: 20rpx;
}

.core-tag {
  font-size: 24rpx;
  color: #4CAF50;
}

.core-bag {
  width: 68rpx;
  height: 32rpx;
  background: #F7F8FB;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
}

.weather-info {
  display: flex;
}

.temperature,
.humidity {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  margin-left: 20rpx;
}

.weather-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 6rpx;
}

.body-tags {
  margin-bottom: 20rpx;
  border-radius: 8rpx;
  border: 2rpx solid #E6E6E6;
  display: inline-flex;
  width: auto;

}

.body-tag {
  padding: 4rpx 22rpx;
  font-size: 22rpx;
  position: relative;
}

.body-tag:not(:last-child)::after {
  content: '';
  position: absolute;
  right: 0;
  top: 8rpx;
  bottom: 8rpx;
  width: 2rpx;
  background-color: #E6E6E6;
}

.daily-suggestion {
  width: 360rpx;
  height: 68rpx;
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  font-size: 24rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 30rpx;
  overflow: hidden;
  text-overflow: ellipsis;
}

.recommendation-icons {

  display: flex;
  width: 324rpx;
  justify-content: space-between;
}

.recommendation-item {
  display: flex;
  flex-direction: column;
  align-items: center;

}

.recommendation-icon {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: 10rpx;
}

.recommendation-item text {
  font-weight: 500;
  font-size: 20rpx;
  color: #999999;
}

/* 体质膳调卡片样式 - 完整版本 */
.diet-plan-card {
  margin: 20rpx;
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 120rpx;
  width: calc(100% - 40rpx);
  box-sizing: border-box;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.06);
}

.diet-plan-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.diet-plan-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.diet-plan-link {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #999;
  gap: 8rpx;
}

.diet-plan-desc {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 30rpx;
}

/* 卡路里显示区域 */
.calories-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40rpx;
  padding: 30rpx 0;
  box-sizing: border-box;
  background: #F8F7F7;
  box-shadow: 0rpx 16rpx 16rpx 0rpx rgba(0, 0, 0, 0.01);
  border-radius: 16rpx 16rpx 16rpx 16rpx;
  height: 176rpx;
  padding: 0 20rpx;
}

.calories-info {
  flex: 1;
}

.calories-label {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 10rpx;
}

.calories-value {
  font-size: 48rpx;
  font-weight: 600;
  color: #333;
}

.calories-unit {
  font-size: 28rpx;
  font-weight: 400;
  color: #666;
}

.calories-progress {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cll {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 60rpx;
  height: 60rpx;
  z-index: 10;
}

.progress-circle {
  position: relative;
  width: 120rpx;
  height: 120rpx;
}

.progress-ring {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: conic-gradient(#4CAF50 0deg, #4CAF50 120deg, #E8F5E8 120deg);
  padding: 8rpx;
}

.progress-ring::after {
  content: '';
  position: absolute;
  top: 8rpx;
  left: 8rpx;
  right: 8rpx;
  bottom: 8rpx;
  background: white;
  border-radius: 50%;
}

.progress-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 2;
}

.progress-icon {
  width: 40rpx;
  height: 40rpx;
}

/* 营养成分区域 */
.nutrition-section {
  margin-bottom: 40rpx;
  display: flex;
  justify-content: space-between;
  gap: 20rpx;
}

.nutrition-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.nutrition-label {
  font-size: 24rpx;
  color: #333;
  margin-bottom: 8rpx;
  font-weight: 500;
}

.nutrition-value {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
}

.nutrition-unit {
  font-size: 24rpx;
  font-weight: 400;
  color: #999;
}

.nutrition-progress {
  width: 100%;
}

/* 居家/外卖选择标签 */
.meal-type-tabs {
  display: flex;
  background-color: #f8f8f8;
  border-radius: 12rpx;
  padding: 6rpx;
  margin-bottom: 30rpx;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 16rpx 0;
  font-size: 26rpx;
  color: #666;
  border-radius: 8rpx;
  transition: all 0.3s ease;
}

.tab-item.active {
  background-color: #4CAF50;
  color: white;
  font-weight: 500;
}

/* 餐次分类 */
.meal-categories {
  margin-top: 20rpx;
}

.meal-category {
  margin-bottom: 40rpx;
}

.category-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.food-list {
  display: flex;
  flex-direction: row;
  gap: 20rpx;
  overflow-x: auto;
  padding-bottom: 10rpx;
  justify-content: space-between;
  align-items: center;
}

.jl {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: 10rpx;
}

.food-image {
  width: 136rpx;
  height: 136rpx;
  border-radius: 12rpx;
  margin-bottom: 12rpx;
  background-color: white;
}

.food-name {
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
  text-align: center;
  margin-bottom: 12rpx;
  line-height: 1.2;
}

.food-desc {
  background: #F7F8FB;
  border-radius: 8rpx 8rpx 8rpx 8rpx;
  padding: 6rpx 14rpx;
  font-weight: 500;
  font-size: 22rpx;
  color: #8BB87F;
}

.record-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6rpx;
  padding: 8rpx 12rpx;
  background-color: white;
  border: 2rpx solid #e0e0e0;
  border-radius: 16rpx;
  font-size: 20rpx;
  color: #999;
  min-width: 80rpx;
  white-space: nowrap;
}

/* 内容区域样式 */
.content-area {
  width: 100%;
  box-sizing: border-box;
  padding-top: 20rpx;
}
.maxCalorie{
  font-size: 48rpx;
  color: #D4D4D4;
}
.calories-unit{
  font-weight: 500;
  font-size: 32rpx;
  color: #D4D4D4;
}


/* 响应式优化 */
@media (max-width: 375px) {
  .menu-item {
    width: 90rpx;
    height: 90rpx;
  }

  .menu-icon {
    width: 90rpx;
    height: 90rpx;
  }

  .robot-image {
    width: 120rpx;
    height: 120rpx;
  }

  .robot-text {
    font-size: 22rpx;
    padding: 5rpx 14rpx;
  }
}

@media (min-width: 414px) {
  .menu-item {
    width: 110rpx;
    height: 110rpx;
  }

  .menu-icon {
    width: 110rpx;
    height: 110rpx;
  }

  .robot-image {
    width: 160rpx;
    height: 160rpx;
  }

  .robot-text {
    font-size: 26rpx;
    padding: 7rpx 18rpx;
  }

}


/* 优化触摸反馈 - 发散光版本 */
.menu-item:active {
  transform: translate(-50%, -50%) scale(0.9);
}

.menu-item:active::before {
  width: 180rpx;
  height: 180rpx;
  background: radial-gradient(circle,
      rgba(64, 224, 255, 0.5) 10%,
      rgba(64, 224, 255, 0.3) 25%,
      rgba(64, 224, 255, 0.2) 40%,
      rgba(64, 224, 255, 0.1) 60%,
      transparent 80%);
  animation: touch-feedback-glow 0.3s ease-out;
}

.menu-item.active:active {
  transform: translate(-50%, -50%) scale(1.15);
}

/* 触摸反馈发光动画 */
@keyframes touch-feedback-glow {
  0% {
    background: radial-gradient(circle,
        rgba(64, 224, 255, 0.8) 5%,
        rgba(64, 224, 255, 0.6) 15%,
        rgba(64, 224, 255, 0.4) 30%,
        rgba(64, 224, 255, 0.2) 50%,
        transparent 70%);
    transform: translate(-50%, -50%) scale(0.8);
  }

  100% {
    background: radial-gradient(circle,
        rgba(64, 224, 255, 0.5) 10%,
        rgba(64, 224, 255, 0.3) 25%,
        rgba(64, 224, 255, 0.2) 40%,
        rgba(64, 224, 255, 0.1) 60%,
        transparent 80%);
    transform: translate(-50%, -50%) scale(1);
  }
}

/* 餐次分类空状态样式 */
.meal-empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 20rpx;
  text-align: center;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 10rpx;
}

.empty-desc {
  font-size: 24rpx;
  color: #ccc;
}

/* food-list-box布局优化 */
.food-list-box{
  display: flex;
  gap: 20rpx;
  flex: 1;
  overflow-x: auto;
  padding-bottom: 10rpx;
  
}

/* 增强食物项目的触摸反馈 */
.food-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 120rpx;
  position: relative;
  padding: 10rpx;
  border-radius: 12rpx;
  transition: all 0.2s ease;
}

.food-item:active {
  background-color: #f0f0f0;
  transform: scale(0.95);
}

/* 记录按钮触摸反馈 */
.jl-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  font-weight: 400;
  font-size: 24rpx;
  color: #666666;
  padding: 10rpx;
  border-radius: 12rpx;
  transition: all 0.2s ease;
}

.jl-box:active {
  background-color: #f0f0f0;
  transform: scale(0.95);
}