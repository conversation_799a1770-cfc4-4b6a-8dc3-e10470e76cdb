<!--index.wxml-->
<view class="scrollarea page-with-tabbar">
  <view class="container">
    <!-- 自定义头部组件 -->
    <custom-header title="餐参Ai" show-logo="{{true}}" show-search="{{true}}" show-user="{{true}}"
      bind:headerinit="onHeaderInit" bind:search="onSearchTap" bind:user="onUserProfileTap">
    </custom-header>

    <!-- 内容区域 -->
    <view class="content-area" style="{{contentStyle}}">
      <!-- 简化轮播图 -->
      <swiper class="health-card-swiper" indicator-dots="{{true}}" autoplay="{{true}}" interval="{{3000}}"
        duration="{{500}}" circular="{{true}}">
        <swiper-item wx:for="{{healthCards}}" wx:key="id">
          <view class="banner-item" data-index="{{index}}" bindtap="onBannerTap">
            <image class="health-card-image" src="{{item.image}}" mode="aspectFill" lazy-load="{{true}}"></image>
          </view>
        </swiper-item>
      </swiper>

      <!-- 功能导航区域 -->
      <view class="function-nav">
        <image class="function-nav-bg" src="/image/home/<USER>" mode="aspectFill"></image>
        <image class="robot" src="/image/home/<USER>" mode="aspectFit|aspectFill|widthFix" lazy-load="false" binderror="" bindload="">

        </image>
        <view class="function-nav-content">
          <!-- 椭圆环形菜单 - 星系旋转版本 -->
          <view
            class="elliptical-menu {{galaxyRotation.isUserDragging ? 'galaxy-rotating' : ''}} {{galaxyRotation.isInertiaScrolling ? 'inertia-scrolling' : ''}} {{isCircleRotating ? 'menu-item-transition' : ''}}"
            bindtouchstart="handleTouchStart" bindtouchmove="handleTouchMove" bindtouchend="handleTouchEnd"
            bindtouchcancel="handleTouchEnd">

            <!-- 太极阴阳背景 -->
            <!-- 太极阴阳背景 -->
            <view class="taiji-background">
              <view class="taiji-container">
                <image class="taiji-image" src="/image/home/<USER>" mode="aspectFill"></image>
              </view>
            </view>

            <!-- 五行能量环 -->
            <view class="wuxing-energy-ring">
              <view class="energy-ring energy-ring-wood"></view>
              <view class="energy-ring energy-ring-fire"></view>
              <view class="energy-ring energy-ring-earth"></view>
              <view class="energy-ring energy-ring-metal"></view>
              <view class="energy-ring energy-ring-water"></view>
            </view>

            <!-- 星宿轨道系统 -->
            <view class="constellation-system">
              <view class="orbit-layer orbit-inner">
                <view class="star-particle star-1"></view>
                <view class="star-particle star-2"></view>
                <view class="star-particle star-3"></view>
              </view>
              <view class="orbit-layer orbit-middle">
                <view class="star-particle star-4"></view>
                <view class="star-particle star-5"></view>
                <view class="star-particle star-6"></view>
                <view class="star-particle star-7"></view>
              </view>
              <view class="orbit-layer orbit-outer">
                <view class="star-particle star-8"></view>
                <view class="star-particle star-9"></view>
              </view>
            </view>

            <!-- 能量流转线条 -->
            <view class="energy-flow-system">
              <view class="energy-line energy-line-1" style="display: {{energyFlowActive ? 'block' : 'none'}}"></view>
              <view class="energy-line energy-line-2" style="display: {{energyFlowActive ? 'block' : 'none'}}"></view>
              <view class="energy-line energy-line-3" style="display: {{energyFlowActive ? 'block' : 'none'}}"></view>
            </view>

            <!-- 动态渲染菜单项 -->
            <view wx:for="{{calculatedMenuPositions}}" wx:key="index"
              class="menu-item {{currentCircleIndex === item.index ? 'active' : ''}} {{isMenuAnimating ? 'animating' : ''}} {{selectionRippleIndex === item.index ? 'ripple' : ''}}"
              style="left: {{item.x}}%; top: {{item.y}}%; transform: translate(-50%, -50%) scale({{item.scale || 1}}); opacity: {{item.opacity || 1}}; filter: blur({{item.blur || 0}}px);"
              bindtap="handleCircleItemClick" data-index="{{item.index}}" data-type="{{item.name}}">

              <!-- 菜单项五行光晕 -->
              <view class="wuxing-aura wuxing-{{item.wuxingType || 'wood'}}"></view>

              <!-- 菜单图标 -->
              <image class="menu-icon" src="{{item.icon}}" mode="aspectFit"></image>

              <!-- 点击涟漪效果 -->
              <view class="ripple-effect" wx:if="{{selectionRippleIndex === item.index}}">
                <view class="ripple-circle ripple-1"></view>
                <view class="ripple-circle ripple-2"></view>
                <view class="ripple-circle ripple-3"></view>
              </view>
            </view>

            <!-- 生命呼吸核心 -->
            <view class="life-core">
              <view class="core-inner"></view>
              <view class="core-middle"></view>
              <view class="core-outer"></view>
            </view>
          </view>
        </view>
      </view>

      <!-- AI体质卡 -->
      <view class="health-analysis-card" bindtap="onBodyCardTap">
        <view class="health-analysis-header">
          <view class="health-analysis-title">AI体质卡</view>
          <view class="health-analysis-days">

            <view class="days-text">距离上次评估已过{{since_last}}天</view>
            <van-icon name="arrow" />
          </view>
        </view>

        <view class="health-analysis-content">
          <view class="health-analysis-core">
            <view class="core-title">饮食改善核心</view>
            <view class="core-tag core-bag">{{solar_term}}</view>
            <view class="weather-info">
              <view class="temperature">
                <image class="weather-icon" src="/image/home/<USER>" mode="aspectFit"></image>
                <text class="core-tag">{{temperature}}°C</text>
              </view>
              <view class="humidity">
                <image class="weather-icon" src="/image/home/<USER>" mode="aspectFit"></image>
                <text class="core-tag">{{humidify}}%</text>
              </view>
            </view>
          </view>

          <view class="body-tags">
            <view class="body-tag">{{constitution_type}}</view>
            <view class="body-tag" wx:for="{{constitution_features}}" wx:key="index">{{item}}</view>
          </view>

          <view class="daily-suggestion">
            <text>{{constitution_suggestions}}</text>
          </view>

          <view class="recommendation-icons">
            <view class="recommendation-item" wx:for="{{constitution_tags}}" wx:key="index">
              <image class="recommendation-icon" src="{{item.icon}}" mode="aspectFit"></image>
              <text>{{item.title}}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 体质膳调 -->
      <view class="diet-plan-card" bindtap="onDietCardTap">
        <!-- 头部 -->
        <view class="diet-plan-header">
          <view class="diet-plan-title">体质膳调</view>
          <view class="diet-plan-link" bindtap="onDietPlanTap" data-diet-key="{{nutritionData.diet_key}}">
            <text>我的膳食计划</text>
            <van-icon name="arrow" size="14px" color="#999" />
          </view>
        </view>
        <view class="diet-plan-desc">精准填平你的健康漏洞</view>

        <!-- 卡路里显示区域 -->
        <view class="calories-section">
          <view class="calories-info">
            <view class="calories-label">今日摄取</view>
            <view class="calories-value">{{nutritionData.calorie}}<text
                class="maxCalorie">/{{nutritionData.maxCalorie}}</text><text class="calories-unit"> kcal</text></view>
          </view>
          <view class="calories-progress">
            <van-circle value="{{ nutritionData.caloriePercent }}" size="70" stroke-width="7" layer-color="#F2F2F2"
              color="#8BB87F" />
            <image src="/image/home/<USER>" mode="aspectFit" class="cll"></image>
          </view>
        </view>

        <!-- 营养成分 -->
        <view class="nutrition-section">
          <view class="nutrition-item">
            <view class="nutrition-label">蛋白质</view>
            <view class="nutrition-value">{{nutritionData.protein}}<text
                class="nutrition-unit">/{{nutritionData.maxProtein}}g</text></view>
            <view class="nutrition-progress">
              <van-progress percentage="{{nutritionData.proteinPercent}}" stroke-width="8" color="#4CAF50"
                track-color="#f0f0f0" show-pivot="{{false}}" />
            </view>
          </view>
          <view class="nutrition-item">
            <view class="nutrition-label">碳水化合物</view>
            <view class="nutrition-value">{{nutritionData.carb}}<text
                class="nutrition-unit">/{{nutritionData.maxCarb}}g</text></view>
            <view class="nutrition-progress">
              <van-progress percentage="{{nutritionData.carbPercent}}" stroke-width="8" color="#4CAF50"
                track-color="#f0f0f0" show-pivot="{{false}}" />
            </view>
          </view>
          <view class="nutrition-item">
            <view class="nutrition-label">脂肪</view>
            <view class="nutrition-value">{{nutritionData.fat}}<text
                class="nutrition-unit">/{{nutritionData.maxFat}}g</text></view>
            <view class="nutrition-progress">
              <van-progress percentage="{{nutritionData.fatPercent}}" stroke-width="8" color="#4CAF50"
                track-color="#f0f0f0" show-pivot="{{false}}" />
            </view>
          </view>
        </view>

        <!-- 居家/外卖选择 -->
        <view class="meal-type-tabs">
          <view class="tab-item active">居家</view>
          <view class="tab-item">外卖</view>
        </view>

        <!-- 餐次分类 -->
        <view class="meal-categories">
          <view class="meal-category" wx:for="{{mealCategories}}" wx:key="key" wx:if="{{familyMeals[item.key].length > 0}}">
            <view class="category-title">{{item.title}}</view>
            <view class="food-list">
              <view class="food-list-box">
                <view class="food-item" 
                      wx:for="{{familyMeals[item.key]}}" 
                      wx:key="index" 
                      wx:for-item="food"
                      bindtap="onFoodItemTap"
                      data-food-data="{{food}}"
                      data-meal-type="{{item.key}}">
                  <image src="{{food.dish_icon}}" class="food-image" mode="aspectFit"></image>
                  <view class="food-name">{{food.dish_name}}</view>
                  <view class="food-desc">{{item.desc}}</view>
                </view>
              </view>

              <view class="jl-box" bindtap="onMealRecordTap" data-meal-type="{{item.key}}">
                <image class="jl" src="/image/home/<USER>" mode="aspectFit"></image>
                <text>记录</text>
              </view>
            </view>
          </view>

          <!-- 空状态提示 -->
          <view class="meal-empty-state" wx:if="{{familyMeals.breakfast.length === 0 && familyMeals.lunch.length === 0 && familyMeals.dinner.length === 0}}">
            <image class="empty-icon" src="/image/home/<USER>" mode="aspectFit"></image>
            <text class="empty-text">暂无推荐餐食</text>
            <text class="empty-desc">点击记录按钮添加您的餐食</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</view>